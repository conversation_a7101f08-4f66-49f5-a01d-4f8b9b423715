import React, { useState } from 'react';

// Import global styles
import '../../styles/IngredientData.css';
import '../../styles/MonthSelection.css';
// Import page-specific styles
import './styles/index.css';
import './styles/SearchBar.css';

// Import page-specific hooks
import { useIngredients } from './hooks/useIngredients';
import { useEditMode } from './hooks/useEditMode';

// Import page-specific components
import TypeTabBar from './components/TypeTabBar';
import SearchBar from './components/SearchBar';
import IngredientTable from './components/IngredientTable';
import IngredientAdder from './components/IngredientAdder/index'; // Import the new component

function Ingredient() {
  // Get ingredients-related state and handlers
  const {
    ingredients,
    setIngredients,
    filteredIngredients,
    activeTypeTab,
    setActiveTypeTab,
    searchTerm,
    setSearchTerm,
    typeOptions,
    measurementOptions,
    formatDivisibleBy,
    formatBoughtByAmount
  } = useIngredients();
  
  // Get edit-related state and handlers
  const {
    editMode,
    editValues,
    saveStatus,
    toggleEditMode,
    handleInputChange,
    saveChanges,
    cancelEdit
  } = useEditMode(ingredients, setIngredients);

  // Collapsible adder state
  const [isAdderCollapsed, setIsAdderCollapsed] = useState(false);

  return (
    <div className="ingredient-container">
      <div className="ingredient-main-layout">
        <div className={`ingredient-adder-section ${isAdderCollapsed ? 'collapsed' : ''}`}>
          <button
            className="collapse-toggle-button"
            onClick={() => setIsAdderCollapsed(!isAdderCollapsed)}
            title={isAdderCollapsed ? 'Expand Ingredient Adder' : 'Collapse Ingredient Adder'}
          >
            {isAdderCollapsed ? '▶' : '◀'}
          </button>
          {!isAdderCollapsed && <IngredientAdder />}
        </div>

        <div className="ingredient-table-section">
          {ingredients.length === 0 ? (
            <p>Loading ingredients...</p>
          ) : (
            <div className="ingredient-list">
              <TypeTabBar
                typeOptions={typeOptions}
                activeTypeTab={activeTypeTab}
                setActiveTypeTab={setActiveTypeTab}
              />

              <SearchBar
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                placeholder="Search ingredients by name..."
              />

              <IngredientTable
                filteredIngredients={filteredIngredients}
                typeOptions={typeOptions}
                measurementOptions={measurementOptions}
                formatDivisibleBy={formatDivisibleBy}
                formatBoughtByAmount={formatBoughtByAmount}
                editMode={editMode}
                editValues={editValues}
                saveStatus={saveStatus}
                toggleEditMode={toggleEditMode}
                handleInputChange={handleInputChange}
                saveChanges={saveChanges}
                cancelEdit={cancelEdit}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Ingredient; 
