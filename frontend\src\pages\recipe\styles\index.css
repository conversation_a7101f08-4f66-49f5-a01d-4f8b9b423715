/* Recipe page specific styles - Unified Design System */
.recipe-container {
  max-width: 95vw;
  margin: 0 auto;
  padding: 20px; /* Consistent 20px padding */
  box-sizing: border-box;
  height: calc(100vh - 60px); /* Account for navbar height */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Keep main container from scrolling */
  position: fixed; /* Make completely rigid */
  top: 60px; /* Account for navbar */
  left: 2.5vw;
  right: 2.5vw;
  bottom: 0;
  background-color: #f5f5f5;
}

.recipe-main-layout {
  display: flex;
  gap: 20px; /* Consistent 20px gap */
  height: 100%;
  width: 100%;
  align-items: stretch;
}

.recipe-adder-section {
  flex: 0 0 45%; /* Consistent proportions */
  display: flex;
  flex-direction: column;
  position: relative;
  transition: flex-basis 0.3s ease;
}

.recipe-adder-section.collapsed {
  flex: 0 0 50px; /* Just enough space for the toggle button */
}

.collapse-toggle-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.collapse-toggle-button:hover {
  background: #0056b3;
  transform: scale(1.1);
}

.recipe-list-section {
  flex: 1;
  background-color: white;
  border-radius: 12px; /* Consistent border radius */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* Consistent shadow */
  padding: 20px; /* Consistent 20px padding */
  box-sizing: border-box;
  overflow: hidden; /* Prevent this section from overflowing */
  display: flex;
  flex-direction: column;
}

/* Header styles - Unified Design System */
.ingredient-header {
  background-color: #26a69a;
  color: white;
  padding: 24px; /* Consistent padding */
  border-radius: 12px; /* Consistent border radius */
  margin-bottom: 20px; /* Consistent margin */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* Consistent shadow */
  text-align: center;
  width: 100%;
  box-sizing: border-box;
}

.ingredient-header h1 {
  margin: 0;
  font-size: 2.2rem; /* Slightly smaller for better proportion */
  font-weight: 600; /* Consistent font weight */
  line-height: 1.2; /* Better line height */
}

.ingredient-header p {
  margin: 12px 0 0; /* Consistent spacing */
  font-size: 1rem; /* Consistent font size */
  opacity: 0.9;
  line-height: 1.4; /* Better line height */
}

.recipe-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Recipe list heading - Unified styling */
.recipe-list h2 {
  text-align: center;
  margin: 0 0 20px 0; /* Consistent margin */
  flex-shrink: 0;
  font-size: 1.5rem; /* Consistent font size */
  font-weight: 600; /* Consistent font weight */
  color: #2c3e50; /* Consistent color */
  line-height: 1.3; /* Better line height */
}

/* Recipe cards grid - Unified spacing */
.recipe-cards {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding: 0; /* Remove padding for cleaner alignment */
  display: grid;
  grid-template-columns: 1fr 1fr; /* Two equal columns by default */
  gap: 16px; /* Consistent 16px gap */
  align-content: start;
  grid-auto-rows: max-content; /* Allow rows to size based on content */
}

/* Single column when adder is expanded (not collapsed) */
.recipe-adder-section:not(.collapsed) ~ .recipe-list-section .recipe-cards {
  grid-template-columns: 1fr; /* Single column when adder is expanded */
}

/* Recipe cards - Unified sizing and spacing */
.recipe-cards .recipe-card.collapsed {
  min-height: 360px; /* Consistent minimum height */
  height: fit-content; /* Size to content */
  width: 100%;
  border-radius: 12px; /* Consistent border radius */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* Consistent shadow */
  background-color: white;
  overflow: hidden;
  transition: all 0.3s ease; /* Smooth transitions */
}

.recipe-cards .recipe-card.expanded {
  min-height: 480px; /* Consistent minimum height when expanded */
  height: fit-content; /* Size to content for full expansion */
  width: 100%;
  border-radius: 12px; /* Consistent border radius */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15); /* Enhanced shadow when expanded */
  background-color: white;
  overflow: hidden;
  transition: all 0.3s ease; /* Smooth transitions */
  transform: translateY(-2px); /* Subtle lift effect */
}

/* Recipe card headers - Unified styling */
.recipe-cards .recipe-header {
  padding: 16px; /* Consistent padding */
  background-color: #f8f9fa; /* Subtle background */
  border-bottom: 1px solid #e9ecef; /* Consistent border */
  margin: 0; /* Remove margin */
}

.recipe-cards .recipe-header h3 {
  font-size: 1.1rem; /* Consistent font size */
  font-weight: 600; /* Consistent font weight */
  color: #2c3e50; /* Consistent color */
  margin: 0 0 8px 0; /* Consistent margin */
  line-height: 1.3; /* Better line height */
}

.recipe-cards .recipe-meta {
  display: flex;
  gap: 12px; /* Consistent gap */
  font-size: 0.85rem; /* Consistent font size */
  color: #6c757d; /* Consistent color */
  flex-wrap: wrap; /* Allow wrapping */
}

/* Recipe card body - Unified styling */
.recipe-cards .recipe-body {
  padding: 16px; /* Consistent padding */
}

.recipe-cards .recipe-ingredients h4 {
  font-size: 1rem; /* Consistent font size */
  font-weight: 600; /* Consistent font weight */
  color: #2c3e50; /* Consistent color */
  margin: 0 0 8px 0; /* Consistent margin */
}

.recipe-cards .recipe-ingredients li {
  font-size: 0.9rem; /* Consistent font size */
  color: #495057; /* Consistent color */
  margin-bottom: 4px; /* Consistent spacing */
  line-height: 1.4; /* Better line height */
}

/* Recipe card footer - Unified styling */
.recipe-cards .recipe-footer {
  padding: 16px; /* Consistent padding */
  margin-top: 0; /* Remove margin */
  border-top: 1px solid #e9ecef; /* Consistent border */
  background-color: #f8f9fa; /* Subtle background */
}

.recipe-cards .recipe-actions button {
  padding: 8px 12px; /* Consistent padding */
  font-size: 0.85rem; /* Consistent font size */
  border-radius: 4px; /* Consistent border radius */
  border: 1px solid #dee2e6; /* Consistent border */
  background-color: white;
  color: #495057;
  transition: all 0.2s ease; /* Smooth transitions */
  margin-right: 8px; /* Consistent spacing */
}

/* Override image uploader height based on card state */
.recipe-cards .recipe-card.collapsed .recipe-image-container {
  height: 180px !important;
  margin-bottom: 8px !important;
}

.recipe-cards .recipe-card.expanded .recipe-image-container {
  height: 250px !important;
  margin-bottom: 12px !important;
}

/* Make recipe card text more compact */
.recipe-cards .recipe-instructions {
  margin-bottom: 8px;
}

.recipe-cards .recipe-instructions p {
  font-size: 0.8rem;
  line-height: 1.3;
  margin-bottom: 4px;
}

/* Hide the original recipe-header styles now that we're using the new header */
.recipe-header-container {
  display: none;
}

/* ServingSizeSelector styling - Unified Design System */
.serving-size-buttons {
  display: flex;
  gap: 8px; /* Consistent gap */
  margin-bottom: 16px; /* Consistent margin */
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.serving-button {
  padding: 10px 16px; /* Consistent padding */
  border: 1px solid #dee2e6; /* Consistent border */
  background-color: #f8f9fa; /* Consistent background */
  border-radius: 6px; /* Consistent border radius */
  cursor: pointer;
  transition: all 0.2s ease; /* Smooth transitions */
  font-size: 0.9rem; /* Consistent font size */
  font-weight: 500; /* Consistent font weight */
}

.serving-button.active {
  background-color: #26a69a; /* Consistent brand color */
  color: white;
  border-color: #26a69a;
  box-shadow: 0 2px 8px rgba(38, 166, 154, 0.2); /* Subtle shadow */
}

/* Action buttons container - Unified styling */
.recipe-action-buttons {
  display: flex;
  gap: 12px; /* Consistent gap */
  justify-content: center;
  margin: 20px 0; /* Consistent margin */
  width: 100%;
  box-sizing: border-box;
  flex-wrap: wrap; /* Allow wrapping */
}

.rounding-button {
  background-color: #4a7c59;
  color: white;
  border: none;
  border-radius: 6px; /* Consistent border radius */
  cursor: pointer;
  transition: all 0.2s ease; /* Smooth transitions */
  padding: 10px 16px; /* Consistent padding */
  font-size: 0.9rem; /* Consistent font size */
  font-weight: 500; /* Consistent font weight */
  box-shadow: 0 2px 8px rgba(74, 124, 89, 0.2); /* Subtle shadow */
}

.rounding-button:hover {
  background-color: #386641;
  transform: translateY(-1px); /* Subtle lift effect */
  box-shadow: 0 4px 12px rgba(74, 124, 89, 0.3); /* Enhanced shadow */
}

.rounding-button:disabled {
  background-color: #95b595;
  cursor: not-allowed;
  transform: none; /* Remove hover effects */
  box-shadow: none; /* Remove shadow */
}

.divisibility-button {
  background-color: #f0ad4e;
  color: white;
  border: none;
  border-radius: 6px; /* Consistent border radius */
  cursor: pointer;
  transition: all 0.2s ease; /* Smooth transitions */
  padding: 10px 16px; /* Consistent padding */
  font-size: 0.9rem; /* Consistent font size */
  font-weight: 500; /* Consistent font weight */
  box-shadow: 0 2px 8px rgba(240, 173, 78, 0.2); /* Subtle shadow */
}

.divisibility-button:hover {
  background-color: #e09e41;        
}

.divisibility-button:disabled {
  background-color: #f0ad4e;
  cursor: not-allowed;
}

.generate-servings-button {
  background-color: #4e8ff0;
  color: white;
  border: none;
  border-radius: 6px; /* Consistent border radius */
  cursor: pointer;
  transition: all 0.2s ease; /* Smooth transitions */
  padding: 10px 16px; /* Consistent padding */
  font-size: 0.9rem; /* Consistent font size */
  font-weight: 500; /* Consistent font weight */
  box-shadow: 0 2px 8px rgba(90, 159, 212, 0.2); /* Subtle shadow */
}

.generate-servings-button:hover {
  background-color: #4181e0;
  transform: translateY(-1px); /* Subtle lift effect */
  box-shadow: 0 4px 12px rgba(90, 159, 212, 0.3); /* Enhanced shadow */
}

.generate-servings-button:disabled {
  background-color: #95b595;
  cursor: not-allowed;
  transform: none; /* Remove hover effects */
  box-shadow: none; /* Remove shadow */
}

/* Responsive adjustments - Unified Design System */
@media (max-width: 768px) {
  .recipe-container {
    padding: 16px; /* Consistent mobile padding */
  }

  .recipe-main-layout {
    gap: 16px; /* Consistent mobile gap */
  }

  .ingredient-header {
    padding: 20px; /* Consistent mobile padding */
    font-size: 1.8rem; /* Smaller mobile font size */
  }

  .recipe-action-buttons {
    flex-direction: column;
    width: 100%;
    gap: 12px; /* Consistent gap */
  }

  .rounding-button,
  .divisibility-button,
  .generate-servings-button {
    width: 100%;
    padding: 12px 16px; /* Slightly larger mobile padding */
  }

  .recipe-cards {
    grid-template-columns: 1fr; /* Single column on mobile */
    gap: 16px; /* Consistent mobile gap */
  }
}
