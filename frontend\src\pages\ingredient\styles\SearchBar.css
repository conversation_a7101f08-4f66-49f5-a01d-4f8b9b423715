.search-bar {
  margin-bottom: 15px;
  flex-shrink: 0;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
  background-color: #f9f9f9;
  transition: all 0.2s;
  outline: none;
}

.search-input:focus {
  border-color: #4CAF50;
  background-color: white;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.search-input::placeholder {
  color: #999;
}

.clear-search-btn {
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.clear-search-btn:hover {
  background-color: #f0f0f0;
  color: #666;
}

.clear-search-btn:active {
  background-color: #e0e0e0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .search-input-container {
    max-width: 100%;
  }
  
  .search-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}
